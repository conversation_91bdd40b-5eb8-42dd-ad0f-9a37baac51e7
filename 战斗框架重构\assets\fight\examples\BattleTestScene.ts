import { BuffModelBeHurtFight } from "../buff/BuffModelBeHurtFight";
import { Character } from "../characters/Character";
import { PlayerSkillFire1 } from "../skills/PlayerSkillFire1";
import { BattleManager } from "../systems/BattleManager";
import { TimelineManager } from "../systems/TimelineManager";
import { Timeline, TimelineNode } from "../timeline/Timeline";
import { DamageTimelineEvent, PlaySoundTimelineEvent, AddBuffTimelineEvent } from "../timeline/TimelineEvents";
import { CharacterCreateInfo, CharacterRole } from "../types/CharacterTypes";
import { ICharacterEvents } from "../types/ICharacter";
import { DamageType } from "../types/IDamage";
import SkillName from "../types/SkillName";


const { ccclass, property } = cc._decorator;

/**
 * 综合战斗测试场景
 * 整合了基础战斗测试、Timeline测试、AI演示等功能
 */
@ccclass
export class BattleTestScene extends cc.Component {
    // UI 组件
    @property(cc.Node)
    uiRoot: cc.Node = null;
    @property(cc.Label)
    statusLabel: cc.Label = null;

    // 基础战斗按钮
    @property(cc.Button)
    startBattleBtn: cc.Button = null;
    @property(cc.Button)
    endBattleBtn: cc.Button = null;
    @property(cc.Button)
    castSkillBtn: cc.Button = null;
    @property(cc.Button)
    addBuffBtn: cc.Button = null;

    // Timeline 测试按钮
    @property(cc.Button)
    testTimelineBtn: cc.Button = null;
    @property(cc.Button)
    testDamageTimelineBtn: cc.Button = null;
    @property(cc.Button)
    testBuffTimelineBtn: cc.Button = null;

    // AI 和多敌人测试按钮
    @property(cc.Button)
    createEnemiesBtn: cc.Button = null;
    @property(cc.Button)
    toggleAIBtn: cc.Button = null;
    @property(cc.Button)
    debugInfoBtn: cc.Button = null;

    // 角色和管理器
    private player: Character = null;
    private enemy: Character = null;
    private enemies: Character[] = [];
    private battleManager: BattleManager = null;
    private timelineManager: TimelineManager = null;

    // 状态变量
    private battleStarted: boolean = false;
    private aiEnabled: boolean = false;
    private testMode: string = "basic"; // basic, timeline, multi

    onLoad() {
        // 初始化管理器
        this.battleManager = BattleManager.getInstance();
        this.timelineManager = this.battleManager.timelineManager;

        // 设置基础战斗按钮事件
        this.startBattleBtn?.node.on('click', this.onStartBattle, this);
        this.endBattleBtn?.node.on('click', this.onEndBattle, this);
        this.castSkillBtn?.node.on('click', this.onCastSkill, this);
        this.addBuffBtn?.node.on('click', this.onAddBuff, this);

        // 设置Timeline测试按钮事件
        this.testTimelineBtn?.node.on('click', this.onTestTimeline, this);
        this.testDamageTimelineBtn?.node.on('click', this.onTestDamageTimeline, this);
        this.testBuffTimelineBtn?.node.on('click', this.onTestBuffTimeline, this);

        // 设置AI和多敌人测试按钮事件
        this.createEnemiesBtn?.node.on('click', this.onCreateEnemies, this);
        this.toggleAIBtn?.node.on('click', this.onToggleAI, this);
        this.debugInfoBtn?.node.on('click', this.onShowDebugInfo, this);

        console.log("综合战斗测试场景已初始化");
    }
    start() {
        this.createPlayer();
        this.createEnemy();
        console.log("角色创建完成");
        this.updateStatus();
    }
    onDestroy() {
        if (this.battleManager) {
            this.battleManager.cleanup();
        }
    }
    update(dt: number) {
        if (this.battleStarted && this.battleManager) {
            // 使用BattleManager统一更新
            this.battleManager.update(dt);
        }
        this.updateStatus();
    }
    /*** 创建玩家*/
    private createPlayer(): void {
        const playerNode = new cc.Node("Player");
        playerNode.parent = this.node;
        playerNode.position = cc.v3(-200, 0, 0);
        this.player = playerNode.addComponent(Character);
        const playerData: CharacterCreateInfo = {
            prefabKey: "player",
            role: CharacterRole.HERO,
            name: "测试玩家",
            worldPosition: cc.v3(-200, 0, 0),
            initialAttributes: {
                hp: 1000,
                maxHp: 1000,
                mp: 200,
                maxMp: 200,
                attack: 100,
                defense: 50,
                attackSpeed: 1.2,
                moveSpeed: 150,
                attackRange: 300,
                criticalRate: 0.1,
                criticalDamage: 1.5,
                hitRate: 0.95,
                dodgeRate: 0.05,
                level: 10,
                experience: 0
            }
        };
        this.player.setCharacterData(playerData);
        // 学习技能
        const fireSkill = new PlayerSkillFire1();
        this.player.learnSkill(fireSkill);
        console.log("玩家创建完成:", this.player.getCharacterInfo());
    }
    /** * 创建敌人 */
    private createEnemy(): void {
        const enemyNode = new cc.Node("Enemy");
        enemyNode.parent = this.node;
        enemyNode.position = cc.v3(200, 0, 0);
        this.enemy = enemyNode.addComponent(Character);
        const enemyData: CharacterCreateInfo = {
            prefabKey: "enemy",
            role: CharacterRole.ENEMY,
            name: "测试敌人",
            worldPosition: cc.v3(200, 0, 0),
            initialAttributes: {
                hp: 500,
                maxHp: 500,
                mp: 100,
                maxMp: 100,
                attack: 80,
                defense: 30,
                attackSpeed: 1.0,
                moveSpeed: 100,
                attackRange: 150,
                criticalRate: 0.05,
                criticalDamage: 1.2,
                hitRate: 0.9,
                dodgeRate: 0.1,
                level: 5,
                experience: 0
            }
        };
        this.enemy.setCharacterData(enemyData);
        console.log("敌人创建完成:", this.enemy.getCharacterInfo());
    }
    /** * 开始战斗 */
    private onStartBattle(): void {
        if (this.battleStarted) {
            console.log("战斗已经开始");
            return;
        }

        if (!this.player || !this.enemy) {
            console.log("角色未创建完成，无法开始战斗");
            return;
        }

        this.battleStarted = true;

        // 使用BattleManager开始战斗
        let participants = [this.player];
        if (this.testMode === "multi" && this.enemies.length > 0) {
            participants.push(...this.enemies);
            console.log(`=== 多敌人战斗开始 (${this.enemies.length}个敌人) ===`);
        } else if (this.enemy) {
            participants.push(this.enemy);
            console.log("=== 单敌人战斗开始 ===");
        }

        this.battleManager.startBattle("test_battle_" + Date.now(), participants);

        // 更新按钮状态
        if (this.castSkillBtn) this.castSkillBtn.interactable = true;
        if (this.addBuffBtn) this.addBuffBtn.interactable = true;
        if (this.startBattleBtn) this.startBattleBtn.interactable = false;
        if (this.endBattleBtn) this.endBattleBtn.interactable = true;
    }

    /**  * 释放技能  */
    private onCastSkill(): void {
        if (!this.battleStarted || !this.player || !this.enemy) {
            console.log("战斗未开始或角色不存在");
            return;
        }
        if (this.player.isDead || this.enemy.isDead) {
            console.log("有角色已死亡，无法释放技能");
            return;
        }
        const success = this.player.castSkill(SkillName.player_skill_fire1, this.enemy.node);
        if (success) {
            console.log(`${this.player.name} 对 ${this.enemy.name} 释放了火球术！`);
        } else {
            console.log("技能释放失败");
        }
    }

    /** * 添加Buff */
    private onAddBuff(): void {
        if (!this.battleStarted || !this.player) {
            console.log("战斗未开始或玩家不存在");
            return;
        }
        if (this.player.isDead) {
            console.log("玩家已死亡，无法添加Buff");
            return;
        }
        const buff = new BuffModelBeHurtFight(this.player, this.player);
        this.player.addBuff(buff);
        console.log(`${this.player.name} 获得了反击Buff！`);
    }

    /** * 更新状态显示 */
    private updateStatus(): void {
        if (!this.statusLabel) return;
        let status = "=== 综合战斗系统测试 ===\n";

        // 玩家信息
        if (this.player) {
            const playerInfo = this.player.getCharacterInfo();
            status += `🛡️ 玩家: ${playerInfo.name}\n`;
            status += `HP: ${this.player.attributes.currentHp}/${this.player.attributes.maxHp}\n`;
            status += `MP: ${this.player.attributes.currentMp || 0}/${this.player.attributes.maxMp || 0}\n`;
            status += `状态: ${playerInfo.isDead ? "💀死亡" : "✅存活"}\n`;
            status += `技能数: ${playerInfo.skillCount} | Buff数: ${playerInfo.buffCount}\n\n`;
        }

        // 敌人信息
        if (this.testMode === "multi" && this.enemies.length > 0) {
            status += `👹 敌人 (${this.enemies.length}个):\n`;
            this.enemies.forEach((enemy, index) => {
                const enemyInfo = enemy.getCharacterInfo();
                status += `${index + 1}. ${enemyInfo.name}: ${enemy.attributes.currentHp}/${enemy.attributes.maxHp} ${enemyInfo.isDead ? "💀" : "✅"}\n`;
            });
            status += "\n";
        } else if (this.enemy) {
            const enemyInfo = this.enemy.getCharacterInfo();
            status += `👹 敌人: ${enemyInfo.name}\n`;
            status += `HP: ${this.enemy.attributes.currentHp}/${this.enemy.attributes.maxHp}\n`;
            status += `状态: ${enemyInfo.isDead ? "💀死亡" : "✅存活"}\n\n`;
        }

        // 测试模式和AI状态
        status += `🎮 测试模式: ${this.testMode}\n`;
        status += `🤖 AI状态: ${this.aiEnabled ? "✅启用" : "❌禁用"}\n\n`;
        if (this.battleManager) {
            const battleStats = this.battleManager.getBattleStats();
            status += `战斗状态:\n`;
            status += `进行中: ${battleStats.isInBattle ? "是" : "否"}\n`;
            status += `时长: ${battleStats.duration.toFixed(1)}s\n`;
            status += `参战者: ${battleStats.participantCount}\n\n`;

            status += `Timeline统计:\n`;
            status += `活跃: ${battleStats.timelineStats.activeCount}\n`;
            status += `暂停: ${battleStats.timelineStats.pausedCount}\n`;
            status += `总执行: ${battleStats.timelineStats.totalExecutedEvents}\n\n`;

            status += `子弹统计:\n`;
            status += `活跃: ${battleStats.bulletStats.activeCount}\n`;
            status += `总创建: ${battleStats.bulletStats.totalCreated}\n`;
            status += `总销毁: ${battleStats.bulletStats.totalDestroyed}\n`;
        }
        this.statusLabel.string = status;
    }

    // ==================== 新增的测试方法 ====================

    /** 结束战斗 */
    private onEndBattle(): void {
        if (!this.battleStarted) {
            console.log("战斗未开始");
            return;
        }

        this.battleManager.endBattle("manual_stop");
        this.battleStarted = false;
        this.aiEnabled = false;

        // 重置按钮状态
        if (this.startBattleBtn) this.startBattleBtn.interactable = true;
        if (this.endBattleBtn) this.endBattleBtn.interactable = false;
        if (this.castSkillBtn) this.castSkillBtn.interactable = false;
        if (this.addBuffBtn) this.addBuffBtn.interactable = false;

        console.log("战斗已结束");
    }

    /** 测试基础Timeline功能 */
    private onTestTimeline(): void {
        if (!this.player || !this.enemy) {
            console.log("角色未准备好");
            return;
        }

        console.log("=== 测试基础Timeline ===");

        // 创建一个简单的Timeline
        const timeline = new Timeline(
            "test_timeline_" + Date.now(),
            "基础测试Timeline",
            3.0,
            this.player,
            this.enemy
        );

        // 添加音效事件
        const soundEvent = new PlaySoundTimelineEvent("sound_1", "test_sound");
        const soundNode = new TimelineNode("sound_node", 0.5, soundEvent);
        timeline.addNode(soundNode);

        // 添加到TimelineManager
        this.timelineManager.addTimeline(timeline);

        console.log("基础Timeline已创建并添加到管理器");
    }

    /** 测试伤害Timeline */
    private onTestDamageTimeline(): void {
        if (!this.player || !this.enemy) {
            console.log("角色未准备好");
            return;
        }

        console.log("=== 测试伤害Timeline ===");

        // 创建伤害Timeline
        const timeline = new Timeline(
            "damage_timeline_" + Date.now(),
            "伤害测试Timeline",
            2.0,
            this.player,
            this.enemy
        );

        // 添加伤害事件
        const damageEvent = new DamageTimelineEvent("damage_1", 80, DamageType.PHYSICAL);
        const damageNode = new TimelineNode("damage_node", 1.0, damageEvent);
        timeline.addNode(damageNode);

        // 添加到TimelineManager
        this.timelineManager.addTimeline(timeline);

        console.log("伤害Timeline已创建，将在1秒后造成80点物理伤害");
    }

    /** 测试Buff Timeline */
    private onTestBuffTimeline(): void {
        if (!this.player || !this.enemy) {
            console.log("角色未准备好");
            return;
        }

        console.log("=== 测试Buff Timeline ===");

        // 创建Buff Timeline
        const timeline = new Timeline(
            "buff_timeline_" + Date.now(),
            "Buff测试Timeline",
            1.5,
            this.player,
            this.enemy
        );

        // 添加Buff事件
        const buffEvent = new AddBuffTimelineEvent("buff_1", "hurt_fight");
        const buffNode = new TimelineNode("buff_node", 0.8, buffEvent);
        timeline.addNode(buffNode);

        // 添加到TimelineManager
        this.timelineManager.addTimeline(timeline);

        console.log("Buff Timeline已创建，将在0.8秒后给敌人添加反击Buff");
    }

    /** 创建多个敌人 */
    private onCreateEnemies(): void {
        console.log("=== 创建多个敌人 ===");

        // 清理现有敌人
        for (const enemy of this.enemies) {
            if (enemy && enemy.node) {
                enemy.node.destroy();
            }
        }
        this.enemies = [];

        // 创建3个敌人
        for (let i = 0; i < 3; i++) {
            const enemyNode = new cc.Node(`Enemy_${i + 1}`);
            enemyNode.parent = this.node;
            enemyNode.position = cc.v3(200 + i * 150, 0, 0);

            const enemy = enemyNode.addComponent(Character);
            const enemyData: CharacterCreateInfo = {
                prefabKey: "enemy",
                role: CharacterRole.ENEMY,
                name: `敌人${i + 1}`,
                worldPosition: cc.v3(200 + i * 150, 0, 0),
                initialAttributes: {
                    hp: 300 + i * 100,
                    maxHp: 300 + i * 100,
                    mp: 50,
                    maxMp: 50,
                    attack: 60 + i * 10,
                    defense: 20 + i * 5,
                    attackSpeed: 1.0,
                    moveSpeed: 80,
                    attackRange: 120,
                    criticalRate: 0.05,
                    criticalDamage: 1.2,
                    hitRate: 0.9,
                    dodgeRate: 0.05,
                    level: 3 + i,
                    experience: 0
                }
            };
            enemy.setCharacterData(enemyData);
            this.setupEnemyEvents(enemy);
            this.enemies.push(enemy);

            console.log(`创建了${enemy.name}，HP: ${enemy.attributes.maxHp}`);
        }

        this.testMode = "multi";
        console.log("多敌人模式已激活");
    }

    /** 切换AI模式 */
    private onToggleAI(): void {
        this.aiEnabled = !this.aiEnabled;

        if (this.aiEnabled) {
            console.log("=== AI模式已启用 ===");
            // 启动AI定时器
            this.schedule(this.updateAI, 1.0);
        } else {
            console.log("=== AI模式已禁用 ===");
            // 停止AI定时器
            this.unschedule(this.updateAI);
        }

        // 更新按钮文本（如果需要的话）
        if (this.toggleAIBtn && this.toggleAIBtn.getComponentInChildren(cc.Label)) {
            const label = this.toggleAIBtn.getComponentInChildren(cc.Label);
            label.string = this.aiEnabled ? "关闭AI" : "开启AI";
        }
    }

    /** 显示调试信息 */
    private onShowDebugInfo(): void {
        console.log("=== 调试信息 ===");

        if (this.battleManager) {
            console.log("BattleManager状态:");
            this.battleManager.printDebugInfo();
        }

        if (this.timelineManager) {
            console.log("TimelineManager状态:");
            this.timelineManager.printDebugInfo();
        }

        if (this.player) {
            console.log("玩家信息:", this.player.getCharacterInfo());
        }

        if (this.enemy) {
            console.log("敌人信息:", this.enemy.getCharacterInfo());
        }

        if (this.enemies.length > 0) {
            console.log("多敌人信息:");
            this.enemies.forEach((enemy, index) => {
                console.log(`敌人${index + 1}:`, enemy.getCharacterInfo());
            });
        }

        console.log("当前测试模式:", this.testMode);
        console.log("AI状态:", this.aiEnabled ? "启用" : "禁用");
    }

    /** 设置敌人事件 */
    private setupEnemyEvents(enemy: Character): void {
        const events: ICharacterEvents = {
            onDeath: (character) => {
                console.log(`${character.characterName} 被击败了！`);
                this.onEnemyDeath(character as Character);
            },
            onTakeDamage: (character, damage, attacker) => {
                console.log(`${character.characterName} 受到了 ${damage} 点伤害`);
                if (attacker) {
                    console.log(`攻击者: ${attacker.characterName}`);
                }
            },
            onSkillCast: (character, skillName) => {
                console.log(`${character.characterName} 释放了技能: ${skillName}`);
            }
        };
        enemy.setEvents(events);
    }

    /** 敌人死亡处理 */
    private onEnemyDeath(enemy: Character): void {
        // 从敌人列表中移除
        const index = this.enemies.indexOf(enemy);
        if (index >= 0) {
            this.enemies.splice(index, 1);
            console.log(`${enemy.name} 已从敌人列表中移除`);
        }

        // 检查是否所有敌人都被击败
        const aliveEnemies = this.enemies.filter(e => !e.isDead);
        if (aliveEnemies.length === 0 && !this.enemy?.isDead) {
            // 只有单个敌人的情况
        } else if (aliveEnemies.length === 0) {
            console.log("🎉 胜利！所有敌人都被击败了！");
            this.onEndBattle();
        }
    }

    /** AI更新逻辑 */
    private updateAI(): void {
        if (!this.aiEnabled || !this.battleStarted || !this.player || this.player.isDead) {
            return;
        }

        // 玩家AI：攻击最近的敌人
        this.updatePlayerAI();

        // 敌人AI：攻击玩家
        this.updateEnemyAI();
    }

    /** 更新玩家AI */
    private updatePlayerAI(): void {
        const target = this.findNearestEnemy();
        if (!target) return;

        // 尝试释放技能
        if (this.player.castSkill(SkillName.player_skill_fire1, target.node)) {
            console.log(`[AI] ${this.player.name} 对 ${target.name} 释放了火球术`);
        } else if (this.player.isInAttackRange && this.player.isInAttackRange(target)) {
            // 如果技能释放失败，使用普通攻击
            this.player.attack(target);
            console.log(`[AI] ${this.player.name} 攻击了 ${target.name}`);
        }
    }

    /** 更新敌人AI */
    private updateEnemyAI(): void {
        // 单个敌人AI
        if (this.enemy && !this.enemy.isDead && this.enemy.isInAttackRange(this.player)) {
            this.enemy.attack(this.player);
            console.log(`[AI] ${this.enemy.name} 攻击了 ${this.player.name}`);
        }

        // 多敌人AI
        for (const enemy of this.enemies) {
            if (!enemy.isDead && enemy.isInAttackRange(this.player)) {
                enemy.attack(this.player);
                console.log(`[AI] ${enemy.name} 攻击了 ${this.player.name}`);
            }
        }
    }

    /** 寻找最近的敌人 */
    private findNearestEnemy(): Character | null {
        let nearestEnemy: Character | null = null;
        let minDistance = Infinity;

        // 检查单个敌人
        if (this.enemy && !this.enemy.isDead) {
            const distance = cc.Vec3.distance(this.player.node.position, this.enemy.node.position);
            if (distance < minDistance) {
                minDistance = distance;
                nearestEnemy = this.enemy;
            }
        }

        // 检查多个敌人
        for (const enemy of this.enemies) {
            if (enemy.isDead) continue;
            const distance = cc.Vec3.distance(this.player.node.position, enemy.node.position);
            if (distance < minDistance) {
                minDistance = distance;
                nearestEnemy = enemy;
            }
        }

        return nearestEnemy;
    }
}
